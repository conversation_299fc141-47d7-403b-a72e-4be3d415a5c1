import numpy as np
from dataprocess.volume import Volume
from predict.adapter import FewshotAdapter

class Prediction:

    def __init__(
        self, adapter: FewshotAdapter, volume: np.ndarray, prompts: dict, current_obj_id
    ):
        super().__init__()
        self.adapter = adapter
        self.volume = volume
        self.prompts = prompts  # 存储临时收集的prompts
        self.current_obj_id = current_obj_id
        # self.result = None  # 存储预测结果
        self.condition_frames = {}  # 存储生成的条件帧

    def generate_condition_frames(self):
        """为所有有提示的方向生成条件帧"""
        # 预处理
        self._prepare_prediction()

        # 为每个方向生成条件帧
        for direction in ["x", "y", "z"]:
            if direction not in self.prompts:
                continue

            # 收集所有有提示的帧索引
            frames_with_points = set(self.prompts[direction].get("points", {}).keys())
            frames_with_boxes = set(self.prompts[direction].get("boxes", {}).keys())
            frames_with_masks = set(self.prompts[direction].get("masks", {}).keys())
            
            # 应用点提示
            for frame_index in frames_with_points:
                for point in self.prompts[direction]["points"][frame_index]:
                    coord, label = point
                    self.adapter.add_point_prompt(
                        frame_index, self.current_obj_id, coord, label, direction
                    )
            
            # 应用框提示
            for frame_index in frames_with_boxes:
                for box in self.prompts[direction]["boxes"][frame_index]:
                    self.adapter.add_box_prompt(
                        frame_index, self.current_obj_id, box, direction
                    )
            
            # 应用掩码提示（仅当该帧没有点或框提示时）
            for frame_index in frames_with_masks:
                if frame_index not in frames_with_points and frame_index not in frames_with_boxes:
                    mask_data = self.prompts[direction]["masks"][frame_index]
                    if np.any(mask_data):
                        self.adapter.add_mask_prompt(
                            frame_index, self.current_obj_id, mask_data, direction
                        )

            # 为当前方向生成条件帧
            self.condition_frames[direction] = {}
            for frame_index in set(
                list(self.prompts[direction].get("points", {}).keys())
                + list(self.prompts[direction].get("boxes", {}).keys())
                + list(self.prompts[direction].get("masks", {}).keys())
            ):
                condition_mask = self.adapter.predict_single_frame(
                    frame_index, direction
                )
                self.condition_frames[direction][frame_index] = condition_mask

        return self.condition_frames

    def run_propagation(self):
        """使用条件帧执行三维传播预测"""
        if not self.condition_frames:
            raise ValueError("No condition frames available. Generate them first.")

        # 确保适配器已加载体积数据
        if not self.adapter.working_volume:
            self._prepare_prediction()

        # 设置所有方向的条件帧
        self.adapter.condition_frames = self.condition_frames

        # 执行传播预测
        return self.adapter.predict()

    def _prepare_prediction(self):
        """将收集的prompts应用到适配器"""
        # 加载体积数据
        volume_obj = Volume()
        volume_obj.set_volume(self.volume)
        if volume_obj.volume.dtype != np.uint8 and volume_obj.volume.dtype != np.uint16:
            print(f"Converting volume from {volume_obj.volume.dtype} to uint8")
            volume_obj.convert_float32_to_uint8()
        self.adapter.load_volume_to_predictor(volume_obj)
volume_dir: "${datasets_root}/em_s0/single"
masks_dir: "${datasets_root}/mito_seg/single"
sam2_config_path: "sam2_config/sam2.1_hiera_t.yaml"
sam2_model_path: "${root}/SAM2/checkpoints/sam2.1_hiera_tiny.pt"
output_dir: "${output_root}/test/new"

state: "fewshot"
keep_in_mem: true
save_masks: true
mask_to_binary: false
label_start: 400
label_stride: 200
resolution: 1024
to_sdf: true
unet_downsample: 2
select_mask_with_obj: true
